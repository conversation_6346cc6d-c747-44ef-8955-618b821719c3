<x-filament-panels::page.simple>
    <div class="space-y-6 w-full">
        {{-- Header Section with Icon --}}
        <div class="text-center">
            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-4"
                 style="background-color: {{ $this->getIconColor() === 'success' ? 'rgb(34 197 94 / 0.1)' : ($this->getIconColor() === 'warning' ? 'rgb(245 158 11 / 0.1)' : 'rgb(239 68 68 / 0.1)') }}">
                @if($this->getIcon() === 'heroicon-o-check-circle')
                    <x-heroicon-o-check-circle class="h-8 w-8 text-success-600" />
                @elseif($this->getIcon() === 'heroicon-o-clock')
                    <x-heroicon-o-clock class="h-8 w-8 text-warning-600" />
                @elseif($this->getIcon() === 'heroicon-o-x-circle')
                    <x-heroicon-o-x-circle class="h-8 w-8 text-danger-600" />
                @elseif($this->getIcon() === 'heroicon-o-exclamation-triangle')
                    <x-heroicon-o-exclamation-triangle class="h-8 w-8 text-danger-600" />
                @else
                    <x-heroicon-o-information-circle class="h-8 w-8 text-gray-600" />
                @endif
            </div>

            <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {{ $this->getHeadingContent() }}
            </h1>

            @if($this->getSubheadingContent())
                <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    {{ $this->getSubheadingContent() }}
                </p>
            @endif
        </div>

        {{-- Payment Information --}}
        @if($this->paymentFound)
            <div class="max-w-4xl mx-auto">
                {{ $this->paymentInfolist }}
            </div>
        @else
            {{-- Error State --}}
            <div class="max-w-2xl mx-auto">
                <x-filament::card>
                    <div class="text-center py-8">
                        <x-heroicon-o-exclamation-triangle class="h-12 w-12 text-danger-600 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            Płatność nie została znaleziona
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">
                            {{ $this->errorMessage }}
                        </p>
                        <div class="space-x-4">
                            <x-filament::button
                                tag="a"
                                href="/app"
                                color="primary"
                            >
                                Powrót do aplikacji
                            </x-filament::button>
                            <x-filament::button
                                tag="a"
                                href="mailto:<EMAIL>"
                                color="gray"
                                outlined
                            >
                                Skontaktuj się z obsługą
                            </x-filament::button>
                        </div>
                    </div>
                </x-filament::card>
            </div>
        @endif

        {{-- Action Buttons for Successful Payments --}}
        @if($this->paymentFound && $this->payment->status === \App\Enums\PaymentStatus::COMPLETED)
            <div class="text-center">
                <div class="space-x-4">
                    <x-filament::button
                        tag="a"
                        href="/app"
                        color="success"
                        size="lg"
                    >
                        <x-heroicon-o-arrow-right class="h-5 w-5 mr-2" />
                        Przejdź do aplikacji
                    </x-filament::button>
                </div>
            </div>
        @endif

        {{-- Additional Actions for Failed Payments --}}
        @if($this->paymentFound && in_array(strtolower($this->payment->status->name), ['failed', 'error']))
            <div class="text-center">
                <div class="space-x-4">
                    <x-filament::button
                        tag="a"
                        href="/app"
                        color="primary"
                    >
                        Spróbuj ponownie
                    </x-filament::button>
                    <x-filament::button
                        tag="a"
                        href="mailto:<EMAIL>"
                        color="gray"
                        outlined
                    >
                        Skontaktuj się z obsługą
                    </x-filament::button>
                </div>
            </div>
        @endif

        {{-- Pending Payment Information --}}
        @if($this->paymentFound && $this->payment->status === \App\Enums\PaymentStatus::PENDING)
            <div class="max-w-2xl mx-auto">
                <x-filament::card>
                    <div class="text-center py-6">
                        <x-heroicon-o-information-circle class="h-8 w-8 text-primary-600 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            Co dalej?
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Twoja płatność jest obecnie przetwarzana przez dostawcę płatności.
                            Proces może potrwać kilka minut. Otrzymasz powiadomienie e-mail
                            po zakończeniu transakcji.
                        </p>
                        <x-filament::button
                            tag="a"
                            href="/app"
                            color="primary"
                        >
                            Przejdź do aplikacji
                        </x-filament::button>
                    </div>
                </x-filament::card>
            </div>
        @endif
    </div>
</x-filament-panels::page.simple>
