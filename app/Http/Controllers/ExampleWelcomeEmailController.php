<?php

namespace App\Http\Controllers;

use App\Events\PaymentCompleted;
use App\Mail\PasswordResetEmail;
use App\Mail\WelcomeEmail;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

class ExampleWelcomeEmailController extends Controller
{

    public function __construct()
    {
        if (app()->environment() !== 'local') {
            abort(404);
        }
    }

    /**
     * Example of sending welcome email after user registration
     */
    public function sendWelcomeEmail()
    {
        // Example user data - in a real application, this would come from your registration process
        $userData = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'username' => 'johndoe'
        ];

        // Send the welcome email
        Mail::to($userData['email'])->send(new WelcomeEmail($userData));

        return 'Welcome email sent!';
    }

    public function sendPassResetEmail()
    {
        // Example user data - in a real application, this would come from your registration process
        $userData = new User([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'username' => 'johndoe'
        ]);

        // Send the welcome email
        Mail::to($userData['email'])->send(new PasswordResetEmail($userData));

        return 'Welcome email sent!';
    }

    public function sendPaymentSuccessEmail()
    {
        $payment = Payment::find(8);
        PaymentCompleted::dispatch($payment);
        return 'Payment email sent!';
    }
}
