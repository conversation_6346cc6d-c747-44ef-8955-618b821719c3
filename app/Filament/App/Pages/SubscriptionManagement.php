<?php

namespace App\Filament\App\Pages;

use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\DTOTenantMetadata;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Repositories\SubscriptionsRepository;
use Filament\Actions\Action;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;

class SubscriptionManagement extends Page implements HasForms, HasInfolists
{
    use InteractsWithForms, InteractsWithInfolists;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static string $view = 'filament.app.pages.subscription-management';
    protected static ?string $navigationGroup = 'Ustawienia';
    protected static ?int $navigationSort = 10;

    public ?Tenant $tenant = null;
    public ?Subscription $currentSubscription = null;
    public Collection $availablePlans;
    public ?DTOTenantMetadata $tenantMetadata = null;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public static function getNavigationLabel(): string
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Zarządzaj swoją subskrypcją i wybierz plan odpowiadający Twoim potrzebom';
    }

    public function mount(): void
    {
        $this->tenant = auth()->user()->getTenant();
        $this->loadCurrentSubscription();
        $this->loadAvailablePlans();
        $this->loadTenantMetadata();
    }

    protected function loadCurrentSubscription(): void
    {
        $this->currentSubscription = SubscriptionsRepository::getActiveSubscription(auth()->user());
        
        // If no active subscription, try to get the most recent one
        if (!$this->currentSubscription) {
            $this->currentSubscription = Subscription::where('user_id', auth()->user()->id)
                ->where('tenant_id', auth()->user()->installation())
                ->latest()
                ->first();
        }
    }

    protected function loadAvailablePlans(): void
    {
        $this->availablePlans = Plan::where('is_active', true)
            ->orderBy('price')
            ->get();
    }

    protected function loadTenantMetadata(): void
    {
        $metaData = $this->tenant?->meta?->meta ?? [];
        
        if (!empty($metaData)) {
            try {
                $this->tenantMetadata = DTOTenantMetadata::make($metaData);
            } catch (\Exception $e) {
                $this->tenantMetadata = null;
            }
        }
    }

    public function currentSubscriptionInfolist(Infolist $infolist): Infolist
    {
        if (!$this->currentSubscription) {
            return $infolist->schema([
                Section::make('Brak aktywnej subskrypcji')
                    ->description('Nie masz obecnie aktywnej subskrypcji. Wybierz plan poniżej, aby rozpocząć.')
                    ->schema([])
            ]);
        }

        return $infolist
            ->record($this->currentSubscription)
            ->schema([
                Section::make('Aktualna subskrypcja')
                    ->description('Szczegóły Twojej obecnej subskrypcji')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('plan.name')
                                    ->label('Plan')
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($state) => match($state) {
                                        SubscriptionStatus::ACTIVE => 'success',
                                        SubscriptionStatus::NEW => 'warning',
                                        SubscriptionStatus::TRIAL => 'info',
                                        SubscriptionStatus::EXPIRED => 'danger',
                                        SubscriptionStatus::CANCELED => 'gray',
                                        default => 'gray'
                                    })
                                    ->formatStateUsing(fn($state) => $state->label()),
                                TextEntry::make('price')
                                    ->label('Cena')
                                    ->formatStateUsing(fn($state) => number_format($state / 100, 2, ',', ' ') . ' PLN'),
                                TextEntry::make('starts_at')
                                    ->label('Data rozpoczęcia')
                                    ->date('d.m.Y'),
                                TextEntry::make('ends_at')
                                    ->label('Data zakończenia')
                                    ->date('d.m.Y'),
                                TextEntry::make('plan.period')
                                    ->label('Okres')
                                    ->formatStateUsing(fn($state) => $state->value . ' miesięcy'),
                            ]),
                        TextEntry::make('plan.description')
                            ->label('Opis planu')
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($record->plan->description)),
                    ])
            ]);
    }

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Show "Continue Payment" button if subscription status is NEW
        if ($this->currentSubscription && $this->currentSubscription->status === SubscriptionStatus::NEW) {
            $latestPayment = $this->currentSubscription->payments()->latest()->first();
            
            if ($latestPayment && $latestPayment->getFinishPaymentLink()) {
                $actions[] = Action::make('continuePayment')
                    ->label('Kontynuuj płatność')
                    ->icon('heroicon-o-credit-card')
                    ->color('warning')
                    ->url($latestPayment->getFinishPaymentLink())
                    ->openUrlInNewTab();
            }
        }

        // Show "Cancel Subscription" button for active subscriptions
        if ($this->currentSubscription && $this->currentSubscription->status === SubscriptionStatus::ACTIVE) {
            $actions[] = Action::make('cancelSubscription')
                ->label('Anuluj subskrypcję')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Anuluj subskrypcję')
                ->modalDescription('Czy na pewno chcesz anulować swoją subskrypcję? Ta akcja nie może zostać cofnięta.')
                ->modalSubmitActionLabel('Tak, anuluj')
                ->action(function () {
                    // TODO: Implement subscription cancellation logic
                    $this->js('alert("Funkcjonalność anulowania subskrypcji zostanie wkrótce dodana.")');
                });
        }

        return $actions;
    }

    public function getAvailablePlansData(): array
    {
        return $this->availablePlans->map(function (Plan $plan) {
            $features = $this->formatPlanFeatures($plan->features ?? []);
            
            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => number_format($plan->price / 100, 2, ',', ' ') . ' PLN',
                'period' => $plan->period->value . ' miesięcy',
                'features' => $features,
                'is_current' => $this->currentSubscription?->plan_id === $plan->id,
                'type' => $plan->type->label(),
            ];
        })->toArray();
    }

    protected function formatPlanFeatures(array $features): array
    {
        $formattedFeatures = [];
        
        foreach ($features as $feature) {
            if (is_numeric($feature)) {
                // Feature is a SystemModules enum value
                $module = SystemModules::tryFrom((int) $feature);
                if ($module) {
                    $formattedFeatures[] = $module->label();
                }
            } elseif (is_string($feature)) {
                // Feature is a custom string
                $formattedFeatures[] = $feature;
            }
        }
        
        return $formattedFeatures;
    }

    public function createNewSubscription(int $planId): void
    {
        // TODO: Implement new subscription creation logic
        $plan = Plan::findOrFail($planId);
        $this->js("alert('Tworzenie nowej subskrypcji dla planu: {$plan->name}. Funkcjonalność zostanie wkrótce dodana.')");
    }
}
