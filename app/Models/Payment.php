<?php

namespace App\Models;

use App\Enums\PaymentStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'tenant_id',
        'subscription_id',
        'hash',
        'provider',
        'provider_payment_id',
        'amount',
        'currency',
        'status',
        'paid_at',
        'payment_method',
        'payment_transaction_id',
        'meta',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
        'meta' => 'array',
        'status' => PaymentStatus::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(PaymentWebhook::class);
    }

    public function getFinishPaymentLink(): string
    {
        return (new $this->provider)->getContinuePaymentLink($this);
    }

    public function save(array $options = [])
    {
        if (blank($this->hash)) {
            $this->hash = (string) Str::ulid();
        }

        return parent::save($options);
    }

    /**
     * Get the payment continuation link for unfinished payments
     */
    public function getFinishPaymentLink(): ?string
    {
        // Only provide continuation link for NEW status payments
        if ($this->status !== \App\Enums\PaymentStatus::NEW) {
            return null;
        }

        // Check if payment has provider payment ID (external payment was initiated)
        if (empty($this->provider_payment_id)) {
            return null;
        }

        // For PayU payments, get the redirect URI from meta data
        if ($this->provider === \App\Services\Payments\PayUPaymentProvider::class) {
            return $this->meta['redirectUri'] ?? null;
        }

        // For other providers, implement as needed
        return null;
    }
}
