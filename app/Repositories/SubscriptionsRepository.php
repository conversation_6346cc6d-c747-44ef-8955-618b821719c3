<?php

namespace App\Repositories;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Services\Payments\InternalPaymentProvider;
use App\Services\Payments\PayUPaymentProvider;

class SubscriptionsRepository
{

    public Subscription $subscription;
    public Plan $plan;
    public bool $isPaymentFinished = false;
    public ?string $continuePaymentUrl = null;


    public static function getActiveSubscription(User $user): ?Subscription
    {
        return Subscription::where('user_id', $user->id)
            ->where('tenant_id', $user->installation())
            ->where('status', SubscriptionStatus::ACTIVE)
            ->first();
    }

    public function createSubscription(User $user, Plan $plan, array $options = []): Subscription
    {
        return Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'plan_id' => $plan->id,
            'status' => SubscriptionStatus::NEW,
            'price' => $plan->price,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($plan->period->value)->endOfDay(),
        ]);
    }

    public function createSubscriptionWithPayment(User $user, Plan $plan, array $options = []): Subscription
    {
        $subscription = $this->createSubscription($user, $plan, $options);
        if ($plan->type === PlanType::TRIAL) {
            $paymentProvider = new InternalPaymentProvider();
            $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
            $this->isPaymentFinished = true;
            $subscription->update([
                'status' => SubscriptionStatus:: ACTIVE
            ]);
            return $subscription;
        }

        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        return $subscription;
    }
}
