<?php

namespace Tests\Feature;

use App\Filament\App\Pages\Auth\ConfirmRegistrationData;
use App\Models\Plan;
use App\Models\Registration;
use Filament\Forms\ComponentContainer;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Livewire\Livewire;
use Tests\TestCase;

class PlanSelectionFormTest extends TestCase
{
    use DatabaseTransactions;

    private Registration $registration;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test registration
        $this->registration = Registration::create([
            'email' => '<EMAIL>',
            'registration_hash' => 'test-hash-123',
            'confirmation_code' => '123456',
            'confirmed_at' => now(),
            'data' => $this->getValidRegistrationData()
        ]);

        // Set session for registration
        Session::put('registration_code', $this->registration->confirmation_code);
    }

    public function test_plan_selection_is_required()
    {
        // Create test plans
        Plan::factory()->active()->count(2)->create();

        // Test form validation without plan selection
        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $formData = $this->getValidRegistrationData();
        unset($formData['selected_plan_id']); // Remove plan selection

        $component->fill($formData);

        // Attempt to submit without plan selection
        $component->call('confirm');

        // Should have validation error
        $component->assertHasErrors(['data.selected_plan_id' => 'required']);
    }

    public function test_plan_selection_with_valid_plan()
    {
        // Create test plan
        $plan = Plan::factory()->active()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $formData = $this->getValidRegistrationData();
        $formData['selected_plan_id'] = $plan->id;

        $component->fill($formData);

        // Should not have validation errors for plan selection
        $component->assertHasNoErrors(['data.selected_plan_id']);
    }

    public function test_plan_options_are_generated_correctly()
    {
        // Create test plans
        $activePlan1 = Plan::factory()->trial()->create();
        $activePlan2 = Plan::factory()->basic()->create();
        $inactivePlan = Plan::factory()->inactive()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        // Get the form component
        $form = $component->instance()->form($component->instance()->makeForm());
        
        // Find the plan selection step
        $wizardSteps = $form->getSchema();
        $planSelectionStep = null;
        
        foreach ($wizardSteps as $component) {
            if ($component instanceof \Filament\Forms\Components\Wizard) {
                $steps = $component->getChildComponents();
                foreach ($steps as $step) {
                    if ($step->getLabel() === 'Wybór planu') {
                        $planSelectionStep = $step;
                        break 2;
                    }
                }
            }
        }

        $this->assertNotNull($planSelectionStep, 'Plan selection step should exist');

        // Test that only active plans are available
        $planOptions = $component->instance()->getPlanOptions();
        
        $this->assertArrayHasKey($activePlan1->id, $planOptions);
        $this->assertArrayHasKey($activePlan2->id, $planOptions);
        $this->assertArrayNotHasKey($inactivePlan->id, $planOptions);
    }

    public function test_plan_descriptions_include_features()
    {
        // Create plan with specific features
        $plan = Plan::factory()->standard()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $planDescriptions = $component->instance()->getPlanDescriptions();

        $this->assertArrayHasKey($plan->id, $planDescriptions);
        $this->assertStringContains($plan->description, $planDescriptions[$plan->id]);
        $this->assertStringContains('Dostępne funkcje:', $planDescriptions[$plan->id]);
        $this->assertStringContains('Faktury sprzedaży', $planDescriptions[$plan->id]);
    }

    public function test_form_validation_messages_are_in_polish()
    {
        Plan::factory()->active()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $formData = $this->getValidRegistrationData();
        unset($formData['selected_plan_id']);

        $component->fill($formData);
        $component->call('confirm');

        // Check that validation message is in Polish
        $errors = $component->instance()->getErrorBag()->getMessages();
        $planSelectionError = $errors['data.selected_plan_id'][0] ?? '';
        
        $this->assertEquals('Wybór planu jest wymagany.', $planSelectionError);
    }

    public function test_wizard_step_structure()
    {
        Plan::factory()->active()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $form = $component->instance()->form($component->instance()->makeForm());
        $schema = $form->getSchema();

        // Find the wizard component
        $wizard = null;
        foreach ($schema as $component) {
            if ($component instanceof \Filament\Forms\Components\Wizard) {
                $wizard = $component;
                break;
            }
        }

        $this->assertNotNull($wizard, 'Wizard component should exist');

        // Check that plan selection step exists
        $steps = $wizard->getChildComponents();
        $stepLabels = array_map(fn($step) => $step->getLabel(), $steps);

        $this->assertContains('Wybór planu', $stepLabels);
        
        // Verify step order (plan selection should be after bank data)
        $bankDataIndex = array_search('Dane bankowe', $stepLabels);
        $planSelectionIndex = array_search('Wybór planu', $stepLabels);
        
        $this->assertNotFalse($bankDataIndex);
        $this->assertNotFalse($planSelectionIndex);
        $this->assertGreaterThan($bankDataIndex, $planSelectionIndex);
    }

    public function test_radio_component_configuration()
    {
        $plan = Plan::factory()->active()->create();

        $component = Livewire::test(ConfirmRegistrationData::class, [
            'hash' => $this->registration->registration_hash
        ]);

        $planSelectionStep = $component->instance()->getPlanSelectionStep();
        $schema = $planSelectionStep->getSchema();

        // Find the section component
        $section = $schema[0];
        $this->assertInstanceOf(\Filament\Forms\Components\Section::class, $section);
        $this->assertEquals('Wybierz plan subskrypcji', $section->getLabel());

        // Find the radio component
        $radioComponent = $section->getSchema()[0];
        $this->assertInstanceOf(\Filament\Forms\Components\Radio::class, $radioComponent);
        $this->assertEquals('selected_plan_id', $radioComponent->getName());
        $this->assertTrue($radioComponent->isRequired());
    }

    private function getValidRegistrationData(): array
    {
        return [
            'user' => [
                'name' => 'Jan',
                'surname' => 'Kowalski',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'adress' => 'ul. Testowa 1',
                'number' => '*********',
            ],
            'company' => [
                'name' => 'Test Company Sp. z o.o.',
                'postcode' => '00-001',
                'city' => 'Warszawa',
                'phone' => '*********',
                'email' => '<EMAIL>',
                'contact_name' => 'Jan Kowalski',
                'website' => 'https://test.com',
                'address' => 'ul. Firmowa 1, Warszawa',
                'tax_residency_country' => 'PL',
                'business_type' => 1,
                'vat_type' => 1,
                'vat_id' => '123-456-78-90',
                'tax_type' => 1,
                'accounting_type' => 1,
                'meta' => [
                    'accounting' => [
                        'regon' => '*********',
                        'bdo' => 'TEST123',
                    ],
                    'bank_accounts' => [
                        [
                            'account_name' => 'Konto główne',
                            'bank_name' => 'Test Bank',
                            'bank_account' => '12 3456 7890 1234 5678 9012 3456',
                            'bank_swift' => 'TESTPL22',
                            'bank_iban' => null,
                            'bank_currency' => 'PLN',
                        ]
                    ]
                ]
            ]
        ];
    }
}
