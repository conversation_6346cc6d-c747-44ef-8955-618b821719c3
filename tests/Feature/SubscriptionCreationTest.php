<?php

namespace Tests\Feature;

use App\Enums\PlanPeriod;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SubscriptionCreationTest extends TestCase
{
    use DatabaseTransactions;

    public function test_subscription_is_created_with_correct_data()
    {
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $plan = Plan::factory()->standard()->create();

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'plan_id' => $plan->id,
            'status' => SubscriptionStatus::ACTIVE,
            'price' => $plan->price,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($plan->period->value)->endOfDay(),
        ]);

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'plan_id' => $plan->id,
            'status' => SubscriptionStatus::ACTIVE->value,
            'price' => $plan->price,
        ]);

        // Test relationships
        $this->assertEquals($user->id, $subscription->user->id);
        $this->assertEquals($tenant->id, $subscription->tenant->id);
        $this->assertEquals($plan->id, $subscription->plan->id);
    }

    public function test_subscription_end_date_calculation()
    {
        $testCases = [
            ['period' => PlanPeriod::MONTH, 'months' => 1],
            ['period' => PlanPeriod::QUARTER, 'months' => 3],
            ['period' => PlanPeriod::HALF_YEAR, 'months' => 6],
            ['period' => PlanPeriod::YEAR, 'months' => 12],
        ];

        foreach ($testCases as $testCase) {
            $plan = Plan::factory()->create(['period' => $testCase['period']]);
            $startDate = now();
            $expectedEndDate = $startDate->copy()->addMonths($testCase['months'])->endOfDay();

            $subscription = Subscription::factory()
                ->withPlan($plan)
                ->create([
                    'starts_at' => $startDate,
                    'ends_at' => $expectedEndDate,
                ]);

            $this->assertEquals(
                $expectedEndDate->format('Y-m-d H:i:s'),
                $subscription->ends_at->format('Y-m-d H:i:s'),
                "End date calculation failed for period: {$testCase['period']->value} months"
            );
        }
    }

    public function test_tenant_configuration_is_updated_with_plan_modules()
    {
        $plan = Plan::factory()->create([
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
                SystemModules::LOGO->value,
            ]
        ]);

        $tenant = Tenant::factory()->create([
            'config' => [
                'modules' => $plan->features,
                'selected_plan_id' => $plan->id,
            ]
        ]);

        // Verify tenant configuration
        $this->assertEquals($plan->features, $tenant->config['modules']);
        $this->assertEquals($plan->id, $tenant->config['selected_plan_id']);

        // Verify tenant has correct modules
        $this->assertTrue($tenant->hasModule(SystemModules::INVOICES));
        $this->assertTrue($tenant->hasModule(SystemModules::SIMPLE_PRODUCTS));
        $this->assertTrue($tenant->hasModule(SystemModules::LOGO));
        $this->assertFalse($tenant->hasModule(SystemModules::WAREHOUSE));
    }

    public function test_tenant_metadata_stores_subscription_info()
    {
        $plan = Plan::factory()->standard()->create();
        $tenant = Tenant::factory()->withMetadata()->create();

        // Simulate storing subscription info in metadata
        $existingMeta = $tenant->meta->meta ?? [];
        $existingMeta['subscription'] = [
            'plan_id' => $plan->id,
            'plan_name' => $plan->name,
            'selected_at' => now()->toISOString(),
        ];

        $tenant->meta->update(['meta' => $existingMeta]);
        $tenant->refresh();

        // Verify subscription info is stored
        $meta = $tenant->meta->meta;
        $this->assertArrayHasKey('subscription', $meta);
        $this->assertEquals($plan->id, $meta['subscription']['plan_id']);
        $this->assertEquals($plan->name, $meta['subscription']['plan_name']);
        $this->assertArrayHasKey('selected_at', $meta['subscription']);
    }

    public function test_subscription_status_enum_casting()
    {
        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);

        $this->assertInstanceOf(SubscriptionStatus::class, $subscription->status);
        $this->assertEquals(SubscriptionStatus::ACTIVE, $subscription->status);

        // Test different statuses
        $statuses = [
            SubscriptionStatus::NEW,
            SubscriptionStatus::ACTIVE,
            SubscriptionStatus::CANCELED,
            SubscriptionStatus::EXPIRED,
            SubscriptionStatus::TRIAL,
        ];

        foreach ($statuses as $status) {
            $subscription = Subscription::factory()->create(['status' => $status]);
            $this->assertEquals($status, $subscription->status);
        }
    }

    public function test_subscription_price_is_copied_from_plan()
    {
        $testPrices = [0, 19.99, 100.00, 999.99];

        foreach ($testPrices as $price) {
            $plan = Plan::factory()->create(['price' => $price]);
            $subscription = Subscription::factory()->withPlan($plan)->create();

            $this->assertEquals($price, $subscription->price);
            $this->assertEquals($plan->price, $subscription->price);
        }
    }

    public function test_subscription_relationships_work_correctly()
    {
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $plan = Plan::factory()->create();

        $subscription = Subscription::factory()
            ->forUserAndTenant($user, $tenant)
            ->withPlan($plan)
            ->create();

        // Test user relationship
        $this->assertEquals($user->id, $subscription->user->id);
        $this->assertEquals($user->email, $subscription->user->email);

        // Test tenant relationship
        $this->assertEquals($tenant->id, $subscription->tenant->id);
        $this->assertEquals($tenant->name, $subscription->tenant->name);

        // Test plan relationship
        $this->assertEquals($plan->id, $subscription->plan->id);
        $this->assertEquals($plan->name, $subscription->plan->name);
    }

    public function test_multiple_subscriptions_for_same_tenant()
    {
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $plan1 = Plan::factory()->create();
        $plan2 = Plan::factory()->create();

        // Create first subscription (expired)
        $subscription1 = Subscription::factory()
            ->forUserAndTenant($user, $tenant)
            ->withPlan($plan1)
            ->expired()
            ->create();

        // Create second subscription (active)
        $subscription2 = Subscription::factory()
            ->forUserAndTenant($user, $tenant)
            ->withPlan($plan2)
            ->active()
            ->create();

        // Verify both subscriptions exist
        $tenantSubscriptions = Subscription::where('tenant_id', $tenant->id)->get();
        $this->assertCount(2, $tenantSubscriptions);

        // Verify active subscription
        $activeSubscription = Subscription::where('tenant_id', $tenant->id)
            ->where('status', SubscriptionStatus::ACTIVE)
            ->first();
        
        $this->assertEquals($subscription2->id, $activeSubscription->id);
        $this->assertEquals($plan2->id, $activeSubscription->plan_id);
    }

    public function test_subscription_factory_states()
    {
        // Test different factory states
        $activeSubscription = Subscription::factory()->active()->create();
        $this->assertEquals(SubscriptionStatus::ACTIVE, $activeSubscription->status);

        $newSubscription = Subscription::factory()->new()->create();
        $this->assertEquals(SubscriptionStatus::NEW, $newSubscription->status);

        $canceledSubscription = Subscription::factory()->canceled()->create();
        $this->assertEquals(SubscriptionStatus::CANCELED, $canceledSubscription->status);

        $expiredSubscription = Subscription::factory()->expired()->create();
        $this->assertEquals(SubscriptionStatus::EXPIRED, $expiredSubscription->status);
        $this->assertTrue($expiredSubscription->ends_at->isPast());

        $trialSubscription = Subscription::factory()->trial()->create();
        $this->assertEquals(SubscriptionStatus::TRIAL, $trialSubscription->status);
        $this->assertNotNull($trialSubscription->trial_ends_at);
    }
}
