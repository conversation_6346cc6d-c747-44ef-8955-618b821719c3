<?php

namespace Tests\Feature;

use App\Enums\Roles;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\Plan;
use App\Models\Registration;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class CompleteRegistrationFlowTest extends TestCase
{
    use DatabaseTransactions;

    public function test_complete_registration_flow_with_plan_selection()
    {
        // Step 1: Create available plans
        $trialPlan = Plan::factory()->trial()->create();
        $basicPlan = Plan::factory()->basic()->create();
        $standardPlan = Plan::factory()->standard()->create();

        // Step 2: Create registration
        $registration = Registration::create([
            'email' => '<EMAIL>',
            'registration_hash' => 'unique-hash-123',
            'confirmation_code' => '123456',
            'confirmed_at' => now(),
            'data' => $this->getCompleteRegistrationData()
        ]);

        // Step 3: Set session (simulating confirmed registration)
        Session::put('registration_code', $registration->confirmation_code);

        // Step 4: Verify no existing data
        $this->assertDatabaseMissing('users', ['email' => $registration->email]);
        $this->assertDatabaseMissing('tenants', ['email' => $registration->email]);
        $this->assertCount(0, Subscription::all());

        // Step 5: Complete registration with plan selection
        $this->completeRegistrationWithPlan($registration, $standardPlan);

        // Step 6: Verify user creation
        $user = User::where('email', $registration->email)->first();
        $this->assertNotNull($user);
        $this->assertEquals($registration->email, $user->email);
        $this->assertTrue($user->active);
        $this->assertTrue($user->hasRole(Roles::TENANT_ADMIN->value));

        // Step 7: Verify user profile creation
        $this->assertNotNull($user->profile);
        $this->assertEquals('Jan', $user->profile->name);
        $this->assertEquals('Kowalski', $user->profile->surname);
        $this->assertEquals('pl', $user->profile->lang);

        // Step 8: Verify tenant creation
        $tenant = Tenant::where('email', $registration->email)->first();
        $this->assertNotNull($tenant);
        $this->assertEquals('Test Company Sp. z o.o.', $tenant->name);
        $this->assertTrue($tenant->is_active);

        // Step 9: Verify tenant configuration
        $this->assertEquals($standardPlan->features, $tenant->config['modules']);
        $this->assertEquals($standardPlan->id, $tenant->config['selected_plan_id']);

        // Step 10: Verify tenant modules
        foreach ($standardPlan->features as $featureId) {
            $module = SystemModules::tryFrom($featureId);
            if ($module) {
                $this->assertTrue($tenant->hasModule($module));
            }
        }

        // Step 11: Verify tenant metadata
        $this->assertNotNull($tenant->meta);
        $meta = $tenant->meta->meta;
        $this->assertArrayHasKey('subscription', $meta);
        $this->assertEquals($standardPlan->id, $meta['subscription']['plan_id']);
        $this->assertEquals($standardPlan->name, $meta['subscription']['plan_name']);

        // Step 12: Verify user-tenant relationship
        $this->assertTrue($user->tenant->contains($tenant));
        $this->assertEquals($tenant->id, $user->installation());

        // Step 13: Verify subscription creation
        $subscription = Subscription::where('user_id', $user->id)
            ->where('tenant_id', $tenant->id)
            ->where('plan_id', $standardPlan->id)
            ->first();

        $this->assertNotNull($subscription);
        $this->assertEquals(SubscriptionStatus::ACTIVE, $subscription->status);
        $this->assertEquals($standardPlan->price, $subscription->price);

        // Step 14: Verify subscription dates
        $this->assertNotNull($subscription->starts_at);
        $this->assertNotNull($subscription->ends_at);
        $expectedEndDate = $subscription->starts_at->copy()
            ->addMonths($standardPlan->period->value)
            ->endOfDay();
        $this->assertEquals(
            $expectedEndDate->format('Y-m-d H:i:s'),
            $subscription->ends_at->format('Y-m-d H:i:s')
        );

        // Step 15: Verify registration completion
        $registration->refresh();
        $this->assertNotNull($registration->finished_at);

        // Step 16: Verify session cleanup would happen
        // (In actual implementation, session would be cleared)
    }

    public function test_registration_flow_with_trial_plan()
    {
        $trialPlan = Plan::factory()->trial()->create();
        
        $registration = Registration::create([
            'email' => '<EMAIL>',
            'registration_hash' => 'trial-hash-123',
            'confirmation_code' => '654321',
            'confirmed_at' => now(),
            'data' => $this->getCompleteRegistrationData()
        ]);

        Session::put('registration_code', $registration->confirmation_code);

        $this->completeRegistrationWithPlan($registration, $trialPlan);

        // Verify trial plan specific behavior
        $subscription = Subscription::where('plan_id', $trialPlan->id)->first();
        $this->assertEquals(0, $subscription->price); // Trial should be free
        
        $tenant = Tenant::where('email', $registration->email)->first();
        $this->assertEquals($trialPlan->features, $tenant->config['modules']);
    }

    public function test_registration_flow_with_different_plan_periods()
    {
        $monthlyPlan = Plan::factory()->create(['period' => \App\Enums\PlanPeriod::MONTH]);
        $yearlyPlan = Plan::factory()->create(['period' => \App\Enums\PlanPeriod::YEAR]);

        foreach ([$monthlyPlan, $yearlyPlan] as $plan) {
            $registration = Registration::create([
                'email' => "test-{$plan->period->value}@example.com",
                'registration_hash' => "hash-{$plan->period->value}",
                'confirmation_code' => '111111',
                'confirmed_at' => now(),
                'data' => $this->getCompleteRegistrationData()
            ]);

            Session::put('registration_code', $registration->confirmation_code);

            $this->completeRegistrationWithPlan($registration, $plan);

            $subscription = Subscription::where('plan_id', $plan->id)->first();
            $expectedEndDate = $subscription->starts_at->copy()
                ->addMonths($plan->period->value)
                ->endOfDay();

            $this->assertEquals(
                $expectedEndDate->format('Y-m-d H:i:s'),
                $subscription->ends_at->format('Y-m-d H:i:s'),
                "End date calculation failed for {$plan->period->value} month period"
            );
        }
    }

    public function test_tenant_metadata_structure_after_registration()
    {
        $plan = Plan::factory()->standard()->create();
        
        $registration = Registration::create([
            'email' => '<EMAIL>',
            'registration_hash' => 'metadata-hash',
            'confirmation_code' => '999999',
            'confirmed_at' => now(),
            'data' => $this->getCompleteRegistrationData()
        ]);

        Session::put('registration_code', $registration->confirmation_code);

        $this->completeRegistrationWithPlan($registration, $plan);

        $tenant = Tenant::where('email', $registration->email)->first();
        $meta = $tenant->meta->meta;

        // Verify original metadata is preserved
        $this->assertArrayHasKey('accounting', $meta);
        $this->assertArrayHasKey('bank_accounts', $meta);
        $this->assertEquals('*********', $meta['accounting']['regon']);

        // Verify subscription metadata is added
        $this->assertArrayHasKey('subscription', $meta);
        $subscriptionMeta = $meta['subscription'];
        $this->assertEquals($plan->id, $subscriptionMeta['plan_id']);
        $this->assertEquals($plan->name, $subscriptionMeta['plan_name']);
        $this->assertArrayHasKey('selected_at', $subscriptionMeta);
    }

    private function getCompleteRegistrationData(): array
    {
        return [
            'user' => [
                'name' => 'Jan',
                'surname' => 'Kowalski',
                'password' => 'securepassword123',
                'password_confirmation' => 'securepassword123',
                'adress' => 'ul. Testowa 1',
                'number' => '*********',
            ],
            'company' => [
                'name' => 'Test Company Sp. z o.o.',
                'postcode' => '00-001',
                'city' => 'Warszawa',
                'phone' => '*********',
                'email' => '<EMAIL>',
                'contact_name' => 'Jan Kowalski',
                'website' => 'https://test.com',
                'address' => 'ul. Firmowa 1, Warszawa',
                'tax_residency_country' => 'PL',
                'business_type' => 1,
                'vat_type' => 1,
                'vat_id' => '123-456-78-90',
                'tax_type' => 1,
                'accounting_type' => 1,
                'meta' => [
                    'accounting' => [
                        'regon' => '*********',
                        'bdo' => 'TEST123',
                    ],
                    'bank_accounts' => [
                        [
                            'account_name' => 'Konto główne',
                            'bank_name' => 'Test Bank',
                            'bank_account' => '12 3456 7890 1234 5678 9012 3456',
                            'bank_swift' => 'TESTPL22',
                            'bank_iban' => null,
                            'bank_currency' => 'PLN',
                        ]
                    ]
                ]
            ]
        ];
    }

    private function completeRegistrationWithPlan(Registration $registration, Plan $plan): void
    {
        // This simulates the ConfirmRegistrationData::confirm() method
        $formData = array_merge($registration->data, [
            'selected_plan_id' => $plan->id
        ]);

        // Simulate the registration completion process
        // (This is the same logic as in RegistrationPlanSelectionTest)
        $dataCollection = collect($formData);
        $companyData = $dataCollection->get('company');
        $userData = $dataCollection->get('user');
        $selectedPlanId = $dataCollection->get('selected_plan_id');

        $selectedPlan = Plan::find($selectedPlanId);
        
        $tenantConfig = [
            'modules' => $selectedPlan->features ?? [SystemModules::INVOICES->value],
            'selected_plan_id' => $selectedPlanId,
        ];

        $tenant = Tenant::create([
            ...collect($companyData)->except('meta')->toArray(),
            'hash' => bin2hex(random_bytes(16)),
            'config' => $tenantConfig,
            'is_active' => true,
        ]);

        $tenant->meta()->create(
            collect($companyData)->only('meta')->toArray()
        );

        $user = User::create([
            'name' => $registration->email,
            'email' => $registration->email,
            'password' => \Illuminate\Support\Facades\Hash::make($userData['password']),
            'active' => true,
        ]);

        $user->assignRole(Roles::TENANT_ADMIN);

        $user->profile()->create([
            ...collect($userData)->except(['password', 'password_confirmation'])->toArray(),
            'lang' => 'pl'
        ]);

        $user->tenant()->attach($tenant->id);

        Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'plan_id' => $selectedPlan->id,
            'status' => SubscriptionStatus::ACTIVE,
            'price' => $selectedPlan->price,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($selectedPlan->period->value)->endOfDay(),
        ]);

        $existingMeta = $tenant->meta?->meta ?? [];
        $existingMeta['subscription'] = [
            'plan_id' => $selectedPlan->id,
            'plan_name' => $selectedPlan->name,
            'selected_at' => now()->toISOString(),
        ];

        $tenant->meta->update(['meta' => $existingMeta]);

        $registration->update(['finished_at' => now()]);
    }
}
